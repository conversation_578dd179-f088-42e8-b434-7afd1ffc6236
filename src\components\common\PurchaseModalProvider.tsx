'use client';

import React from 'react';
import { usePurchaseModal, type PurchaseModalOptions } from '@/hooks/usePurchaseModal';
import { PurchaseModal } from './PurchaseModal';

interface PurchaseModalProviderProps {
  children: React.ReactNode;
  options?: PurchaseModalOptions;
}

/**
 * 购买弹窗提供者组件
 * 提供一个完整的购买弹窗解决方案，包含状态管理和UI
 */
export function PurchaseModalProvider({ 
  children, 
  options = {} 
}: PurchaseModalProviderProps) {
  const {
    isOpen,
    selectedPlan,
    selectedBilling,
    isLoading,
    openModal,
    closeModal,
    selectPlan,
    selectBilling,
    handlePurchase,
  } = usePurchaseModal(options);

  return (
    <>
      {children}
      <PurchaseModal
        isOpen={isOpen}
        onClose={closeModal}
        selectedPlan={selectedPlan}
        selectedBilling={selectedBilling}
        isLoading={isLoading}
        onSelectPlan={selectPlan}
        onSelectBilling={selectBilling}
        onPurchase={handlePurchase}
      />
    </>
  );
}

/**
 * 购买弹窗Hook组合
 * 返回一个完整的购买弹窗解决方案
 */
export function usePurchaseModalWithComponent(options: PurchaseModalOptions = {}) {
  const modalState = usePurchaseModal(options);

  const PurchaseModalComponent = React.useMemo(
    () => (
      <PurchaseModal
        isOpen={modalState.isOpen}
        onClose={modalState.closeModal}
        selectedPlan={modalState.selectedPlan}
        selectedBilling={modalState.selectedBilling}
        isLoading={modalState.isLoading}
        onSelectPlan={modalState.selectPlan}
        onSelectBilling={modalState.selectBilling}
        onPurchase={modalState.handlePurchase}
      />
    ),
    [modalState]
  );

  return {
    ...modalState,
    PurchaseModalComponent,
  };
}
