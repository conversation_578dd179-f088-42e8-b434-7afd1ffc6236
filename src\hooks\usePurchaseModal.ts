'use client';

import { useState, useCallback } from 'react';

export interface PlanType {
  id: 'pro' | 'pro-plus';
  name: string;
  price: string;
  originalPrice: string;
  features: string[];
  isRecommended?: boolean;
}

export interface PurchaseModalOptions {
  defaultPlan?: 'pro' | 'pro-plus';
  defaultBilling?: 'monthly' | 'yearly';
  onPurchase?: (plan: PlanType, billing: 'monthly' | 'yearly') => void;
  onClose?: () => void;
}

export interface PurchaseModalState {
  isOpen: boolean;
  selectedPlan: 'pro' | 'pro-plus';
  selectedBilling: 'monthly' | 'yearly';
  isLoading: boolean;
}

export function usePurchaseModal(options: PurchaseModalOptions = {}) {
  const {
    defaultPlan = 'pro-plus',
    defaultBilling = 'yearly',
    onPurchase,
    onClose,
  } = options;

  const [state, setState] = useState<PurchaseModalState>({
    isOpen: false,
    selectedPlan: defaultPlan,
    selectedBilling: defaultBilling,
    isLoading: false,
  });

  const openModal = useCallback((plan?: 'pro' | 'pro-plus') => {
    setState(prev => ({
      ...prev,
      isOpen: true,
      selectedPlan: plan || prev.selectedPlan,
    }));
  }, []);

  const closeModal = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
      isLoading: false,
    }));
    onClose?.();
  }, [onClose]);

  const selectPlan = useCallback((plan: 'pro' | 'pro-plus') => {
    setState(prev => ({
      ...prev,
      selectedPlan: plan,
    }));
  }, []);

  const selectBilling = useCallback((billing: 'monthly' | 'yearly') => {
    setState(prev => ({
      ...prev,
      selectedBilling: billing,
    }));
  }, []);

  const handlePurchase = useCallback(async (plan: PlanType) => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      await onPurchase?.(plan, state.selectedBilling);
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [onPurchase, state.selectedBilling]);

  return {
    ...state,
    openModal,
    closeModal,
    selectPlan,
    selectBilling,
    handlePurchase,
  };
}
