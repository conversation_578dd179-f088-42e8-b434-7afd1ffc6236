'use client';

import React from 'react';
import { PurchaseModalContainer } from '@/components/common/PurchaseModalSimple';
import { 
  SimplePurchaseExample, 
  QuickPurchaseButton, 
  FeatureLockPrompt 
} from '@/components/common/SimplePurchaseExample';

/**
 * 简单购买弹窗测试页面
 * 展示最简单的使用方式
 */
export default function SimplePurchaseTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 1. 在应用根部添加容器组件 - 只需要添加一次 */}
      <PurchaseModalContainer />
      
      <div className="max-w-4xl mx-auto py-8 px-4 space-y-12">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            超级简单的购买弹窗
          </h1>
          <p className="text-gray-600">
            无需Hook，无需状态管理，一行代码搞定！
          </p>
        </div>

        {/* 完整示例 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <SimplePurchaseExample />
        </section>

        {/* 快速组件示例 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">快速组件</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">快速购买按钮</h3>
              <div className="flex gap-4">
                <QuickPurchaseButton>
                  默认升级
                </QuickPurchaseButton>
                <QuickPurchaseButton 
                  plan="pro" 
                  billing="monthly"
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  Pro月付
                </QuickPurchaseButton>
                <QuickPurchaseButton 
                  plan="pro-plus" 
                  billing="yearly"
                  className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-gray-900"
                  onSuccess={(plan) => {
                    console.log('购买成功回调:', plan);
                  }}
                >
                  Pro+年付
                </QuickPurchaseButton>
              </div>
            </div>
          </div>
        </section>

        {/* 功能限制示例 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">功能限制提示</h2>
          <div className="space-y-4">
            <FeatureLockPrompt 
              featureName="AI背景去除" 
              requiredPlan="pro"
            />
            <FeatureLockPrompt 
              featureName="批量处理" 
              requiredPlan="pro-plus"
            />
          </div>
        </section>

        {/* 实际使用场景 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">实际使用场景</h2>
          <div className="grid md:grid-cols-2 gap-6">
            
            {/* 场景1: 导航栏 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">导航栏升级按钮</h3>
              <div className="bg-gray-900 p-3 rounded flex justify-between items-center">
                <span className="text-white text-sm">PixPretty</span>
                <QuickPurchaseButton 
                  size="sm"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                >
                  升级
                </QuickPurchaseButton>
              </div>
            </div>

            {/* 场景2: 功能页面 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">功能页面</h3>
              <div className="bg-gray-100 p-3 rounded">
                <p className="text-sm text-gray-600 mb-2">AI背景去除工具</p>
                <FeatureLockPrompt 
                  featureName="高级AI工具" 
                  requiredPlan="pro-plus"
                />
              </div>
            </div>

            {/* 场景3: 卡片推广 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">推广卡片</h3>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border">
                <h4 className="font-medium text-gray-900 mb-1">
                  🚀 解锁全部功能
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  升级享受无限制使用
                </p>
                <QuickPurchaseButton 
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  立即升级
                </QuickPurchaseButton>
              </div>
            </div>

            {/* 场景4: 试用提醒 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">试用提醒</h3>
              <div className="bg-orange-50 border border-orange-200 p-4 rounded-lg">
                <h4 className="font-medium text-orange-800 mb-1">
                  ⏰ 试用期剩余3天
                </h4>
                <p className="text-sm text-orange-600 mb-3">
                  升级以继续使用所有功能
                </p>
                <QuickPurchaseButton 
                  size="sm"
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  立即续费
                </QuickPurchaseButton>
              </div>
            </div>
          </div>
        </section>

        {/* 代码示例 */}
        <section className="bg-gray-900 text-white rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">代码示例</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2 text-yellow-400">1. 应用根部设置（只需一次）</h3>
              <pre className="bg-gray-800 p-3 rounded text-sm overflow-x-auto">
{`// 在 app/layout.tsx 或 _app.tsx 中添加
import { PurchaseModalContainer } from '@/components/common/PurchaseModalSimple';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <PurchaseModalContainer />
      </body>
    </html>
  );
}`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2 text-yellow-400">2. 任何地方使用（一行代码）</h3>
              <pre className="bg-gray-800 p-3 rounded text-sm overflow-x-auto">
{`import { showPurchaseModal } from '@/components/common/PurchaseModalSimple';

// 最简单的使用
const handleUpgrade = () => {
  showPurchaseModal();
};

// 带参数的使用
const handleUpgradeWithOptions = () => {
  showPurchaseModal({
    defaultPlan: 'pro-plus',
    defaultBilling: 'yearly',
    onPurchase: async (plan) => {
      // 你的购买逻辑
      await purchaseAPI.createOrder(plan);
    },
  });
};`}
              </pre>
            </div>
          </div>
        </section>

        {/* 优势说明 */}
        <section className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-green-900 mb-4">
            ✨ 这种方式的优势
          </h2>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-green-800">
            <div className="space-y-2">
              <p>✅ <strong>零学习成本</strong> - 一行代码搞定</p>
              <p>✅ <strong>无状态管理</strong> - 不需要Hook或Provider</p>
              <p>✅ <strong>全局可用</strong> - 任何地方都能调用</p>
              <p>✅ <strong>自动处理</strong> - 关闭、加载等都自动处理</p>
            </div>
            <div className="space-y-2">
              <p>✅ <strong>类型安全</strong> - 完整的TypeScript支持</p>
              <p>✅ <strong>响应式设计</strong> - 自动适配桌面和移动端</p>
              <p>✅ <strong>国际化支持</strong> - 支持多语言</p>
              <p>✅ <strong>Promise支持</strong> - 可以用async/await</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
