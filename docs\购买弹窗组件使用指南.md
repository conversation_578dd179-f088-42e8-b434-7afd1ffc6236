# 购买弹窗组件使用指南

## 概述

购买弹窗组件是一个完整的购买流程解决方案，包含了状态管理、UI展示和用户交互。组件基于Figma设计稿实现，支持桌面端和移动端的响应式设计。

## 功能特性

- 🎨 **设计还原**: 完全按照Figma设计稿实现，支持桌面端和移动端
- 🔧 **自定义Hook**: 提供`usePurchaseModal`钩子管理弹窗状态
- 📱 **响应式设计**: 自动适配桌面端和移动端布局
- 🎯 **类型安全**: 完整的TypeScript类型定义
- 🔄 **状态管理**: 内置计划选择、计费周期切换等状态管理
- 🎪 **多种使用方式**: 支持Hook、Provider、组合组件等多种使用模式

## 组件结构

```
src/
├── hooks/
│   └── usePurchaseModal.ts          # 购买弹窗状态管理Hook
├── components/common/
│   ├── PurchaseModal.tsx            # 购买弹窗UI组件
│   ├── PurchaseModalProvider.tsx    # 购买弹窗提供者组件
│   ├── PurchaseModalExample.tsx     # 使用示例组件
│   └── index.ts                     # 统一导出
```

## 基础使用

### 方式1: 使用组合Hook (推荐)

```tsx
import { usePurchaseModalWithComponent } from '@/components/common';

function MyComponent() {
  const { openModal, PurchaseModalComponent } = usePurchaseModalWithComponent({
    defaultPlan: 'pro-plus',
    defaultBilling: 'yearly',
    onPurchase: async (plan, billing) => {
      // 处理购买逻辑
      console.log('购买:', plan, billing);
    },
  });

  return (
    <div>
      <button onClick={() => openModal()}>
        升级到Premium
      </button>
      {PurchaseModalComponent}
    </div>
  );
}
```

### 方式2: 使用Provider组件

```tsx
import { PurchaseModalProvider } from '@/components/common';

function App() {
  return (
    <PurchaseModalProvider
      options={{
        onPurchase: async (plan, billing) => {
          // 全局购买处理逻辑
        },
      }}
    >
      <YourAppContent />
    </PurchaseModalProvider>
  );
}
```

### 方式3: 直接使用Hook和组件

```tsx
import { usePurchaseModal, PurchaseModal } from '@/components/common';

function MyComponent() {
  const {
    isOpen,
    openModal,
    closeModal,
    selectedPlan,
    selectedBilling,
    selectPlan,
    selectBilling,
    handlePurchase,
    isLoading,
  } = usePurchaseModal({
    onPurchase: async (plan, billing) => {
      // 购买逻辑
    },
  });

  return (
    <div>
      <button onClick={() => openModal('pro-plus')}>
        升级
      </button>
      
      <PurchaseModal
        isOpen={isOpen}
        onClose={closeModal}
        selectedPlan={selectedPlan}
        selectedBilling={selectedBilling}
        isLoading={isLoading}
        onSelectPlan={selectPlan}
        onSelectBilling={selectBilling}
        onPurchase={handlePurchase}
      />
    </div>
  );
}
```

## 快速组件

### 购买按钮

```tsx
import { PurchaseButton } from '@/components/common';

function Header() {
  return (
    <PurchaseButton 
      plan="pro-plus"
      className="bg-yellow-400 text-black"
    >
      升级到Pro+
    </PurchaseButton>
  );
}
```

### 功能升级提示

```tsx
import { FeatureUpgradePrompt } from '@/components/common';

function AdvancedFeature() {
  const isProUser = false; // 从用户状态获取

  if (!isProUser) {
    return (
      <FeatureUpgradePrompt
        featureName="AI背景去除"
        requiredPlan="pro"
        onUpgrade={() => {
          // 升级成功后的处理
        }}
      />
    );
  }

  return <YourAdvancedFeature />;
}
```

## API参考

### usePurchaseModal

```tsx
interface PurchaseModalOptions {
  defaultPlan?: 'pro' | 'pro-plus';
  defaultBilling?: 'monthly' | 'yearly';
  onPurchase?: (plan: PlanType, billing: 'monthly' | 'yearly') => void;
  onClose?: () => void;
}

interface PurchaseModalState {
  isOpen: boolean;
  selectedPlan: 'pro' | 'pro-plus';
  selectedBilling: 'monthly' | 'yearly';
  isLoading: boolean;
}
```

### PlanType

```tsx
interface PlanType {
  id: 'pro' | 'pro-plus';
  name: string;
  price: string;
  originalPrice: string;
  features: string[];
  isRecommended?: boolean;
}
```

## 自定义配置

### 修改计划配置

在`PurchaseModal.tsx`中的`PLANS_CONFIG`对象可以修改计划的价格、功能等信息：

```tsx
const PLANS_CONFIG = {
  pro: {
    id: 'pro' as const,
    name: 'Pro',
    monthly: {
      price: 'US$4.99/mo',
      originalPrice: '$16.9 billed monthly.',
    },
    // ... 其他配置
  },
  // ...
};
```

### 样式自定义

组件使用Tailwind CSS构建，可以通过传递`className`属性来自定义样式：

```tsx
<PurchaseModal
  // ... 其他props
  className="custom-modal-styles"
/>
```

## 集成购买API

```tsx
const { openModal, PurchaseModalComponent } = usePurchaseModalWithComponent({
  onPurchase: async (plan, billing) => {
    try {
      // 调用后端API创建订单
      const response = await fetch('/api/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          planId: plan.id,
          billing,
          userId: currentUser.id,
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        // 跳转到支付页面
        window.location.href = result.paymentUrl;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('购买失败:', error);
      // 显示错误提示
    }
  },
});
```

## 注意事项

1. **响应式设计**: 组件会自动根据屏幕尺寸切换桌面端和移动端布局
2. **状态管理**: 弹窗状态是独立的，不会影响其他组件
3. **类型安全**: 所有接口都有完整的TypeScript类型定义
4. **性能优化**: 使用React.memo和useCallback优化性能
5. **可访问性**: 基于Radix UI构建，具有良好的可访问性支持

## 故障排除

### 弹窗不显示
- 检查`isOpen`状态是否正确
- 确认Dialog组件的z-index设置

### 样式问题
- 确认Tailwind CSS配置正确
- 检查CSS变量是否定义

### 类型错误
- 确认导入的类型定义正确
- 检查TypeScript配置
