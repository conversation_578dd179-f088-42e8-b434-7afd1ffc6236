'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { showPurchaseModal } from './PurchaseModalSimple';

/**
 * 超级简单的购买弹窗使用示例
 */
export function SimplePurchaseExample() {
  // 最简单的使用方式 - 只需要一行代码
  const handleUpgrade = () => {
    showPurchaseModal();
  };

  // 带参数的使用方式
  const handleUpgradeWithOptions = () => {
    showPurchaseModal({
      defaultPlan: 'pro-plus',
      defaultBilling: 'yearly',
      onPurchase: async (plan) => {
        console.log('用户购买了:', plan);
        // 这里可以调用你的购买API
        // await fetch('/api/purchase', { method: 'POST', body: JSON.stringify(plan) });
        alert(`购买成功: ${plan.name} - ${plan.price}`);
      },
    });
  };

  // 使用Promise方式处理结果
  const handleUpgradeWithPromise = async () => {
    try {
      const result = await showPurchaseModal({
        defaultPlan: 'pro',
        onPurchase: async (plan) => {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 2000));
          return plan;
        },
      });
      
      if (result) {
        console.log('购买完成:', result);
      } else {
        console.log('用户取消了购买');
      }
    } catch (error) {
      console.error('购买失败:', error);
    }
  };

  return (
    <div className="p-8 space-y-6">
      <h2 className="text-2xl font-bold">超级简单的购买弹窗</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">1. 最简单的使用 - 一行代码</h3>
          <Button onClick={handleUpgrade}>
            升级 (默认设置)
          </Button>
          <p className="text-sm text-gray-600 mt-1">
            只需要调用 <code>showPurchaseModal()</code>
          </p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">2. 带参数的使用</h3>
          <Button onClick={handleUpgradeWithOptions} variant="outline">
            升级到Pro+ (年付)
          </Button>
          <p className="text-sm text-gray-600 mt-1">
            可以设置默认计划、计费周期和购买回调
          </p>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">3. Promise方式处理结果</h3>
          <Button onClick={handleUpgradeWithPromise} variant="secondary">
            升级并处理结果
          </Button>
          <p className="text-sm text-gray-600 mt-1">
            使用 async/await 处理购买结果
          </p>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 mb-2">使用说明</h4>
        <div className="text-sm text-blue-800 space-y-1">
          <p>1. 在你的应用根部添加 <code>&lt;PurchaseModalContainer /&gt;</code></p>
          <p>2. 在任何地方调用 <code>showPurchaseModal()</code> 即可显示弹窗</p>
          <p>3. 弹窗会自动处理所有内部状态，包括关闭按钮</p>
          <p>4. 完全不需要管理任何状态或Hook</p>
        </div>
      </div>
    </div>
  );
}

/**
 * 快速购买按钮 - 封装了showPurchaseModal的按钮组件
 */
export function QuickPurchaseButton({ 
  children = "升级", 
  plan = 'pro-plus',
  billing = 'yearly',
  onSuccess,
  ...buttonProps 
}: {
  children?: React.ReactNode;
  plan?: 'pro' | 'pro-plus';
  billing?: 'monthly' | 'yearly';
  onSuccess?: (plan: any) => void;
} & React.ComponentProps<typeof Button>) {
  
  const handleClick = () => {
    showPurchaseModal({
      defaultPlan: plan,
      defaultBilling: billing,
      onPurchase: async (purchasedPlan) => {
        // 这里可以集成真实的购买逻辑
        console.log('购买:', purchasedPlan);
        onSuccess?.(purchasedPlan);
      },
    });
  };

  return (
    <Button onClick={handleClick} {...buttonProps}>
      {children}
    </Button>
  );
}

/**
 * 功能限制组件 - 当用户使用受限功能时显示
 */
export function FeatureLockPrompt({ 
  featureName, 
  requiredPlan = 'pro-plus' 
}: {
  featureName: string;
  requiredPlan?: 'pro' | 'pro-plus';
}) {
  const handleUnlock = () => {
    showPurchaseModal({
      defaultPlan: requiredPlan,
      onPurchase: async (plan) => {
        alert(`解锁成功！现在可以使用 ${featureName} 功能了`);
        // 这里可以刷新页面或更新用户状态
      },
    });
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-yellow-800">
            🔒 {featureName} 功能已锁定
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            升级到 {requiredPlan === 'pro' ? 'Pro' : 'Pro+'} 计划解锁此功能
          </p>
        </div>
        <Button
          size="sm"
          onClick={handleUnlock}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900"
        >
          立即解锁
        </Button>
      </div>
    </div>
  );
}
