'use client';

import React from 'react';
import { 
  PurchaseModalExample,
  PurchaseButton,
  FeatureUpgradePrompt 
} from '@/components/common';

/**
 * 购买弹窗测试页面
 * 用于测试和演示购买弹窗组件的各种使用方式
 */
export default function TestPurchasePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-12">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            购买弹窗组件测试页面
          </h1>
          <p className="text-gray-600">
            测试和演示购买弹窗组件的各种使用方式
          </p>
        </div>

        {/* 完整示例 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">完整示例</h2>
          <PurchaseModalExample />
        </section>

        {/* 快速购买按钮 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">快速购买按钮</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">不同计划的购买按钮</h3>
              <div className="flex gap-4">
                <PurchaseButton plan="pro">
                  升级到 Pro
                </PurchaseButton>
                <PurchaseButton 
                  plan="pro-plus"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                >
                  升级到 Pro+
                </PurchaseButton>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-2">不同样式的按钮</h3>
              <div className="flex gap-4">
                <PurchaseButton 
                  plan="pro-plus"
                  variant="outline"
                  size="sm"
                >
                  小按钮
                </PurchaseButton>
                <PurchaseButton 
                  plan="pro-plus"
                  size="lg"
                >
                  大按钮
                </PurchaseButton>
              </div>
            </div>
          </div>
        </section>

        {/* 功能升级提示 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">功能升级提示</h2>
          <div className="space-y-4">
            <FeatureUpgradePrompt
              featureName="AI背景去除"
              requiredPlan="pro"
              onUpgrade={() => {
                console.log('用户升级了AI背景去除功能');
              }}
            />
            
            <FeatureUpgradePrompt
              featureName="批量处理"
              requiredPlan="pro-plus"
              onUpgrade={() => {
                console.log('用户升级了批量处理功能');
              }}
            />
          </div>
        </section>

        {/* 使用场景示例 */}
        <section className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">使用场景示例</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {/* 场景1: 功能限制 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">场景1: 功能限制提示</h3>
              <p className="text-sm text-gray-600 mb-3">
                当用户尝试使用高级功能时显示升级提示
              </p>
              <div className="bg-gray-100 p-3 rounded border-2 border-dashed border-gray-300">
                <p className="text-sm text-gray-500 mb-2">
                  🔒 此功能需要Pro+计划
                </p>
                <FeatureUpgradePrompt
                  featureName="高级AI工具"
                  requiredPlan="pro-plus"
                />
              </div>
            </div>

            {/* 场景2: 导航栏升级按钮 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">场景2: 导航栏升级按钮</h3>
              <p className="text-sm text-gray-600 mb-3">
                在导航栏或工具栏中放置升级按钮
              </p>
              <div className="bg-gray-900 p-3 rounded flex justify-between items-center">
                <span className="text-white text-sm">PixPretty</span>
                <PurchaseButton 
                  plan="pro-plus"
                  size="sm"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                >
                  升级
                </PurchaseButton>
              </div>
            </div>

            {/* 场景3: 卡片式推广 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">场景3: 卡片式推广</h3>
              <p className="text-sm text-gray-600 mb-3">
                在页面中展示升级卡片
              </p>
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
                <h4 className="font-medium text-gray-900 mb-1">
                  🚀 解锁更多功能
                </h4>
                <p className="text-sm text-gray-600 mb-3">
                  升级到Pro+，享受10G存储和所有AI工具
                </p>
                <PurchaseButton 
                  plan="pro-plus"
                  size="sm"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                >
                  立即升级
                </PurchaseButton>
              </div>
            </div>

            {/* 场景4: 试用结束提示 */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium mb-2">场景4: 试用结束提示</h3>
              <p className="text-sm text-gray-600 mb-3">
                当用户试用期结束时的升级提示
              </p>
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <h4 className="font-medium text-red-800 mb-1">
                  ⏰ 试用期即将结束
                </h4>
                <p className="text-sm text-red-600 mb-3">
                  您的试用期将在3天后结束，升级以继续使用所有功能
                </p>
                <PurchaseButton 
                  plan="pro"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  立即升级
                </PurchaseButton>
              </div>
            </div>
          </div>
        </section>

        {/* 开发说明 */}
        <section className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-900 mb-4">
            开发说明
          </h2>
          <div className="text-sm text-blue-800 space-y-2">
            <p>• 打开浏览器开发者工具查看控制台输出</p>
            <p>• 点击购买按钮会触发onPurchase回调</p>
            <p>• 组件支持桌面端和移动端响应式设计</p>
            <p>• 所有交互都有完整的TypeScript类型支持</p>
          </div>
        </section>
      </div>
    </div>
  );
}
