'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { usePurchaseModalWithComponent } from './PurchaseModalProvider';
import { type PlanType } from '@/hooks/usePurchaseModal';

/**
 * 购买弹窗使用示例组件
 * 展示如何使用购买弹窗的不同方式
 */
export function PurchaseModalExample() {
  // 方式1: 使用组合Hook
  const { openModal, PurchaseModalComponent } = usePurchaseModalWithComponent({
    defaultPlan: 'pro-plus',
    defaultBilling: 'yearly',
    onPurchase: async (plan: PlanType, billing) => {
      console.log('购买计划:', plan, '计费周期:', billing);
      
      // 这里可以调用实际的购买API
      // await purchaseAPI.createOrder({ plan, billing });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert(`成功购买 ${plan.name} 计划 (${billing})`);
    },
    onClose: () => {
      console.log('弹窗关闭');
    },
  });

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">购买弹窗示例</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">方式1: 使用组合Hook</h3>
          <div className="space-x-4">
            <Button onClick={() => openModal('pro')}>
              购买 Pro 计划
            </Button>
            <Button onClick={() => openModal('pro-plus')}>
              购买 Pro+ 计划
            </Button>
            <Button onClick={() => openModal()}>
              打开购买弹窗 (默认计划)
            </Button>
          </div>
        </div>
      </div>

      {/* 渲染弹窗组件 */}
      {PurchaseModalComponent}
    </div>
  );
}

/**
 * 简单的购买按钮组件
 * 可以直接在任何地方使用
 */
export function PurchaseButton({ 
  plan = 'pro-plus',
  children = 'Upgrade Now',
  className = '',
  ...props 
}: {
  plan?: 'pro' | 'pro-plus';
  children?: React.ReactNode;
  className?: string;
} & React.ComponentProps<typeof Button>) {
  const { openModal, PurchaseModalComponent } = usePurchaseModalWithComponent({
    defaultPlan: plan,
    onPurchase: async (selectedPlan: PlanType, billing) => {
      // 这里集成实际的购买逻辑
      console.log('购买:', selectedPlan, billing);
      
      // 示例: 跳转到支付页面
      // window.location.href = `/checkout?plan=${selectedPlan.id}&billing=${billing}`;
    },
  });

  return (
    <>
      <Button 
        onClick={() => openModal(plan)} 
        className={className}
        {...props}
      >
        {children}
      </Button>
      {PurchaseModalComponent}
    </>
  );
}

/**
 * 功能受限提示组件
 * 当用户尝试使用高级功能时显示
 */
export function FeatureUpgradePrompt({ 
  featureName,
  requiredPlan = 'pro-plus',
  onUpgrade,
}: {
  featureName: string;
  requiredPlan?: 'pro' | 'pro-plus';
  onUpgrade?: () => void;
}) {
  const { openModal, PurchaseModalComponent } = usePurchaseModalWithComponent({
    defaultPlan: requiredPlan,
    onPurchase: async (plan: PlanType, billing) => {
      onUpgrade?.();
      // 处理升级逻辑
    },
  });

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-yellow-800">
            升级到 {requiredPlan === 'pro' ? 'Pro' : 'Pro+'} 解锁 {featureName}
          </h3>
          <p className="text-sm text-yellow-700 mt-1">
            此功能需要 {requiredPlan === 'pro' ? 'Pro' : 'Pro+'} 计划才能使用
          </p>
        </div>
        <Button
          size="sm"
          onClick={() => openModal(requiredPlan)}
          className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900"
        >
          立即升级
        </Button>
      </div>
      {PurchaseModalComponent}
    </div>
  );
}
