'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Check, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
} from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/base';

export interface PurchaseOptions {
  defaultPlan?: 'pro' | 'pro-plus';
  defaultBilling?: 'monthly' | 'yearly';
  onPurchase?: (plan: {
    id: 'pro' | 'pro-plus';
    name: string;
    price: string;
    billing: 'monthly' | 'yearly';
  }) => Promise<void> | void;
}

// 全局弹窗状态
let globalModalState = {
  isOpen: false,
  options: {} as PurchaseOptions,
  resolve: null as ((value: any) => void) | null,
};

// 全局弹窗组件实例
let globalSetModalState: React.Dispatch<React.SetStateAction<typeof globalModalState>> | null = null;

/**
 * 显示购买弹窗的全局方法
 */
export function showPurchaseModal(options: PurchaseOptions = {}): Promise<any> {
  return new Promise((resolve) => {
    if (globalSetModalState) {
      globalSetModalState({
        isOpen: true,
        options,
        resolve,
      });
    } else {
      console.warn('PurchaseModal not mounted. Please add <PurchaseModalContainer /> to your app.');
      resolve(null);
    }
  });
}

/**
 * 关闭购买弹窗的全局方法
 */
export function closePurchaseModal() {
  if (globalSetModalState) {
    globalSetModalState(prev => ({
      ...prev,
      isOpen: false,
    }));
  }
}

/**
 * 购买弹窗容器组件 - 需要在应用根部添加一次
 */
export function PurchaseModalContainer() {
  const [modalState, setModalState] = useState(globalModalState);
  const t = useTranslations('purchase');

  // 注册全局状态更新函数
  React.useEffect(() => {
    globalSetModalState = setModalState;
    return () => {
      globalSetModalState = null;
    };
  }, []);

  const [selectedPlan, setSelectedPlan] = useState<'pro' | 'pro-plus'>(
    modalState.options.defaultPlan || 'pro-plus'
  );
  const [selectedBilling, setSelectedBilling] = useState<'monthly' | 'yearly'>(
    modalState.options.defaultBilling || 'yearly'
  );
  const [isLoading, setIsLoading] = useState(false);

  // 重置状态当弹窗打开时
  React.useEffect(() => {
    if (modalState.isOpen) {
      setSelectedPlan(modalState.options.defaultPlan || 'pro-plus');
      setSelectedBilling(modalState.options.defaultBilling || 'yearly');
      setIsLoading(false);
    }
  }, [modalState.isOpen, modalState.options]);

  const getPlanConfig = (t: any) => ({
    pro: {
      id: 'pro' as const,
      name: t('pro'),
      monthly: { price: t('pricing.proMonthly'), originalPrice: t('pricing.proBilledMonthly') },
      yearly: { price: t('pricing.proYearly'), originalPrice: t('pricing.proBilledYearly') },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage2G'),
      ],
    },
    'pro-plus': {
      id: 'pro-plus' as const,
      name: t('proPlus'),
      monthly: { price: t('pricing.proPlusMonthly'), originalPrice: t('pricing.proPlusBilledMonthly') },
      yearly: { price: t('pricing.proPlusYearly'), originalPrice: t('pricing.proPlusBilledYearly') },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage10G'),
      ],
    },
  });

  const PLANS_CONFIG = getPlanConfig(t);

  const handleClose = () => {
    setModalState(prev => ({ ...prev, isOpen: false }));
    if (modalState.resolve) {
      modalState.resolve(null);
    }
  };

  const handlePurchase = async () => {
    setIsLoading(true);
    
    try {
      const planConfig = PLANS_CONFIG[selectedPlan];
      const billingConfig = planConfig[selectedBilling];
      
      const purchaseData = {
        id: planConfig.id,
        name: planConfig.name,
        price: billingConfig.price,
        billing: selectedBilling,
      };

      if (modalState.options.onPurchase) {
        await modalState.options.onPurchase(purchaseData);
      }

      if (modalState.resolve) {
        modalState.resolve(purchaseData);
      }
      
      handleClose();
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={modalState.isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-[1005px] max-h-[90vh] overflow-y-auto p-0 bg-white rounded-[40px] border-0 shadow-[0px_4px_24px_0px_rgba(0,0,0,0.25)]">
        {/* Desktop Layout */}
        <div className="hidden md:flex">
          {/* Left Side - Features */}
          <div className="w-[543px] bg-white p-12 rounded-l-[40px]">
            {/* Product Preview */}
            <div className="mb-8 h-[204px] bg-gradient-to-r from-orange-100 to-yellow-100 rounded-lg relative overflow-hidden">
              <div className="absolute inset-0 bg-[url('/images/product-preview.jpg')] bg-cover bg-center opacity-80" />
              <div className="absolute top-4 right-4 w-5 h-5 bg-white/20 rounded-md flex items-center justify-center">
                <div className="w-2 h-2 bg-gray-600 rounded-full" />
              </div>
            </div>

            {/* Features List */}
            <div className="space-y-5">
              <h3 className="text-base font-bold text-gray-900">{t('features')}</h3>
              <div className="space-y-3">
                {PLANS_CONFIG['pro-plus'].features.map((feature, index) => (
                  <div key={index} className="text-xs text-gray-900 leading-relaxed">
                    {feature}
                  </div>
                ))}
              </div>
            </div>

            {/* Plan Comparison */}
            <div className="mt-8 flex gap-8">
              {/* Pro Column */}
              <div className="flex-1">
                <h4 className="text-base font-bold text-center text-gray-900 mb-5">
                  {t('pro')}
                </h4>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((_, index) => (
                    <div key={index} className="flex justify-center">
                      <X className="w-6 h-6 text-gray-900" strokeWidth={1.5} />
                    </div>
                  ))}
                  <div className="flex justify-center py-1">
                    <span className="text-base font-bold text-gray-900">2G</span>
                  </div>
                  <div className="flex justify-center">
                    <X className="w-6 h-6 text-gray-900" strokeWidth={1.5} />
                  </div>
                </div>
              </div>

              {/* Pro+ Column */}
              <div className="flex-1 bg-yellow-50 border border-orange-200 rounded-xl p-4">
                <h4 className="text-base font-bold text-center text-gray-900 mb-5">
                  {t('proPlus')}
                </h4>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5, 7].map((_, index) => (
                    <div key={index} className="flex justify-center">
                      <Check className="w-6 h-6 text-gray-900" strokeWidth={2} />
                    </div>
                  ))}
                  <div className="flex justify-center py-1">
                    <span className="text-base font-bold text-gray-900">10G</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Purchase */}
          <div className="w-[462px] bg-gray-900 p-12 rounded-r-[40px] text-white">
            <div className="space-y-16">
              {/* Header */}
              <div>
                <h2 className="text-2xl font-bold leading-tight mb-4">
                  {t('upgradeToPixPrettyPremium')}
                </h2>
                
                {/* Billing Toggle */}
                <div className="flex rounded-xl overflow-hidden border border-gray-600">
                  <button
                    onClick={() => setSelectedBilling('monthly')}
                    className={cn(
                      'flex-1 px-12 py-2 text-base font-light transition-colors',
                      selectedBilling === 'monthly'
                        ? 'bg-white text-gray-900'
                        : 'bg-transparent text-white border-r border-gray-600'
                    )}
                  >
                    {t('monthly')}
                  </button>
                  <button
                    onClick={() => setSelectedBilling('yearly')}
                    className={cn(
                      'flex-1 px-14 py-3 text-base font-light transition-colors relative',
                      selectedBilling === 'yearly'
                        ? 'bg-gray-600 text-white'
                        : 'bg-transparent text-white'
                    )}
                  >
                    {t('yearly')}
                    {selectedBilling === 'yearly' && (
                      <span className="absolute -top-1 right-2 text-xs text-orange-400">
                        {t('save25Percent')}
                      </span>
                    )}
                  </button>
                </div>
              </div>

              {/* Plan Cards */}
              <div className="space-y-3">
                {/* Pro+ Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro-plus'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-800 border-gray-600'
                  )}
                  onClick={() => setSelectedPlan('pro-plus')}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xl font-bold">{PLANS_CONFIG['pro-plus'].name}</h3>
                      <p className="text-sm font-light text-gray-300">
                        {PLANS_CONFIG['pro-plus'][selectedBilling].originalPrice}
                      </p>
                    </div>
                    <div className="text-lg font-light">
                      {PLANS_CONFIG['pro-plus'][selectedBilling].price}
                    </div>
                  </div>
                </div>

                {/* Pro Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-800 border-gray-600'
                  )}
                  onClick={() => setSelectedPlan('pro')}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xl font-bold">{PLANS_CONFIG.pro.name}</h3>
                      <p className="text-sm font-light text-gray-300">
                        {PLANS_CONFIG.pro[selectedBilling].originalPrice}
                      </p>
                    </div>
                    <div className="text-lg font-light">
                      {PLANS_CONFIG.pro[selectedBilling].price}
                    </div>
                  </div>
                </div>
              </div>

              {/* Purchase Button */}
              <div className="space-y-2">
                <Button
                  onClick={handlePurchase}
                  disabled={isLoading}
                  className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-light text-lg py-3 rounded-full border border-yellow-400/20 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.25)]"
                >
                  {isLoading ? t('processing') : t('buyNow')}
                </Button>
                <p className="text-sm font-light text-gray-400 text-center">
                  {t('notSureYet')}
                  <br />
                  {t('checkPricingPage')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout - 简化版本 */}
        <div className="md:hidden bg-gray-900 text-white rounded-[24px] max-w-[358px] mx-auto p-5">
          <h2 className="text-xl font-bold mb-4">{t('upgradeToPixPrettyPremium')}</h2>
          
          {/* Plan Selection */}
          <div className="space-y-3 mb-6">
            {(['pro-plus', 'pro'] as const).map((planId) => (
              <div
                key={planId}
                className={cn(
                  'p-3 rounded-xl border cursor-pointer transition-all',
                  selectedPlan === planId
                    ? 'bg-yellow-50/5 border-yellow-400'
                    : 'bg-gray-800 border-gray-600'
                )}
                onClick={() => setSelectedPlan(planId)}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-bold">{PLANS_CONFIG[planId].name}</h3>
                    <p className="text-xs text-gray-300">
                      {PLANS_CONFIG[planId][selectedBilling].originalPrice}
                    </p>
                  </div>
                  <div className="text-base font-light">
                    {PLANS_CONFIG[planId][selectedBilling].price}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Purchase Button */}
          <Button
            onClick={handlePurchase}
            disabled={isLoading}
            className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-light py-3 rounded-full"
          >
            {isLoading ? t('processing') : t('buyNow')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
