'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Check, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/base';
import {
  type PlanType,
  type PurchaseModalState,
} from '@/hooks/usePurchaseModal';

interface PurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPlan: 'pro' | 'pro-plus';
  selectedBilling: 'monthly' | 'yearly';
  isLoading: boolean;
  onSelectPlan: (plan: 'pro' | 'pro-plus') => void;
  onSelectBilling: (billing: 'monthly' | 'yearly') => void;
  onPurchase: (plan: PlanType) => void;
}

// 将计划配置移到组件内部以支持国际化
function getPlanConfig(t: any) {
  return {
    pro: {
      id: 'pro' as const,
      name: t('pro'),
      monthly: {
        price: t('pricing.proMonthly'),
        originalPrice: t('pricing.proBilledMonthly'),
      },
      yearly: {
        price: t('pricing.proYearly'),
        originalPrice: t('pricing.proBilledYearly'),
      },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage2G'),
      ],
    },
    'pro-plus': {
      id: 'pro-plus' as const,
      name: t('proPlus'),
      monthly: {
        price: t('pricing.proPlusMonthly'),
        originalPrice: t('pricing.proPlusBilledMonthly'),
      },
      yearly: {
        price: t('pricing.proPlusYearly'),
        originalPrice: t('pricing.proPlusBilledYearly'),
      },
      features: [
        t('features.powerfulEditingTools'),
        t('features.aiTools'),
        t('features.aiPortraitEditing'),
        t('features.removeWatermark'),
        t('features.aiSlidesGenerating'),
        t('features.storage10G'),
      ],
    },
  };
}

export function PurchaseModal({
  isOpen,
  onClose,
  selectedPlan,
  selectedBilling,
  isLoading,
  onSelectPlan,
  onSelectBilling,
  onPurchase,
}: PurchaseModalProps) {
  const t = useTranslations('purchase');
  const PLANS_CONFIG = getPlanConfig(t);

  const handlePurchase = () => {
    const planConfig = PLANS_CONFIG[selectedPlan];
    const billingConfig = planConfig[selectedBilling];

    const plan: PlanType = {
      id: planConfig.id,
      name: planConfig.name,
      price: billingConfig.price,
      originalPrice: billingConfig.originalPrice,
      features: planConfig.features,
      isRecommended: selectedPlan === 'pro-plus',
    };

    onPurchase(plan);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-[1005px] max-h-[90vh] overflow-y-auto p-0 bg-white rounded-[40px] border-0 shadow-[0px_4px_24px_0px_rgba(0,0,0,0.25)]'>
        {/* Desktop Layout */}
        <div className='hidden md:flex'>
          {/* Left Side - Features */}
          <div className='w-[543px] bg-white p-12 rounded-l-[40px]'>
            {/* Product Preview */}
            <div className='mb-8 h-[204px] bg-gradient-to-r from-orange-100 to-yellow-100 rounded-lg relative overflow-hidden'>
              <div className="absolute inset-0 bg-[url('/images/product-preview.jpg')] bg-cover bg-center opacity-80" />
              <div className='absolute top-4 right-4 w-5 h-5 bg-white/20 rounded-md flex items-center justify-center'>
                <div className='w-2 h-2 bg-gray-600 rounded-full' />
              </div>
            </div>

            {/* Features List */}
            <div className='space-y-5'>
              <h3 className='text-base font-bold text-gray-900'>
                {t('features')}
              </h3>
              <div className='space-y-3'>
                {PLANS_CONFIG['pro-plus'].features.map((feature, index) => (
                  <div
                    key={index}
                    className='text-xs text-gray-900 leading-relaxed'
                  >
                    {feature}
                  </div>
                ))}
              </div>
            </div>

            {/* Plan Comparison */}
            <div className='mt-8 flex gap-8'>
              {/* Pro Column */}
              <div className='flex-1'>
                <h4 className='text-base font-bold text-center text-gray-900 mb-5'>
                  Pro
                </h4>
                <div className='space-y-3'>
                  {[1, 2, 3, 4, 5].map((_, index) => (
                    <div key={index} className='flex justify-center'>
                      <X className='w-6 h-6 text-gray-900' strokeWidth={1.5} />
                    </div>
                  ))}
                  <div className='flex justify-center py-1'>
                    <span className='text-base font-bold text-gray-900'>
                      2G
                    </span>
                  </div>
                  <div className='flex justify-center'>
                    <X className='w-6 h-6 text-gray-900' strokeWidth={1.5} />
                  </div>
                </div>
              </div>

              {/* Pro+ Column */}
              <div className='flex-1 bg-yellow-50 border border-orange-200 rounded-xl p-4'>
                <h4 className='text-base font-bold text-center text-gray-900 mb-5'>
                  Pro+
                </h4>
                <div className='space-y-3'>
                  {[1, 2, 3, 4, 5, 7].map((_, index) => (
                    <div key={index} className='flex justify-center'>
                      <Check
                        className='w-6 h-6 text-gray-900'
                        strokeWidth={2}
                      />
                    </div>
                  ))}
                  <div className='flex justify-center py-1'>
                    <span className='text-base font-bold text-gray-900'>
                      10G
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Purchase */}
          <div className='w-[462px] bg-gray-900 p-12 rounded-r-[40px] text-white'>
            <div className='space-y-16'>
              {/* Header */}
              <div>
                <h2 className='text-2xl font-bold leading-tight mb-4'>
                  {t('upgradeToPixPrettyPremium')}
                </h2>

                {/* Billing Toggle */}
                <div className='flex rounded-xl overflow-hidden border border-gray-600'>
                  <button
                    onClick={() => onSelectBilling('monthly')}
                    className={cn(
                      'flex-1 px-12 py-2 text-base font-light transition-colors',
                      selectedBilling === 'monthly'
                        ? 'bg-white text-gray-900'
                        : 'bg-transparent text-white border-r border-gray-600'
                    )}
                  >
                    {t('monthly')}
                  </button>
                  <button
                    onClick={() => onSelectBilling('yearly')}
                    className={cn(
                      'flex-1 px-14 py-3 text-base font-light transition-colors relative',
                      selectedBilling === 'yearly'
                        ? 'bg-gray-600 text-white'
                        : 'bg-transparent text-white'
                    )}
                  >
                    {t('yearly')}
                    {selectedBilling === 'yearly' && (
                      <span className='absolute -top-1 right-2 text-xs text-orange-400'>
                        {t('save25Percent')}
                      </span>
                    )}
                  </button>
                </div>
              </div>

              {/* Plan Cards */}
              <div className='space-y-3'>
                {/* Pro+ Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro-plus'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-800 border-gray-600'
                  )}
                  onClick={() => onSelectPlan('pro-plus')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3 className='text-xl font-bold'>Pro+</h3>
                      <p className='text-sm font-light text-gray-300'>
                        {
                          PLANS_CONFIG['pro-plus'][selectedBilling]
                            .originalPrice
                        }
                      </p>
                    </div>
                    <div className='text-lg font-light'>
                      {PLANS_CONFIG['pro-plus'][selectedBilling].price}
                    </div>
                  </div>
                </div>

                {/* Pro Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-800 border-gray-600'
                  )}
                  onClick={() => onSelectPlan('pro')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3 className='text-xl font-bold'>Pro</h3>
                      <p className='text-sm font-light text-gray-300'>
                        {PLANS_CONFIG.pro[selectedBilling].originalPrice}
                      </p>
                    </div>
                    <div className='text-lg font-light'>
                      {PLANS_CONFIG.pro[selectedBilling].price}
                    </div>
                  </div>
                </div>
              </div>

              {/* Purchase Button */}
              <div className='space-y-2'>
                <Button
                  onClick={handlePurchase}
                  disabled={isLoading}
                  className='w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-light text-lg py-3 rounded-full border border-yellow-400/20 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.25)]'
                >
                  {isLoading ? t('processing') : t('buyNow')}
                </Button>
                <p className='text-sm font-light text-gray-400 text-center'>
                  {t('notSureYet')}
                  <br />
                  {t('checkPricingPage')}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout */}
        <div className='md:hidden bg-gray-900 text-white rounded-[24px] max-w-[358px] mx-auto relative'>
          {/* Close Button */}
          <button
            onClick={onClose}
            className='absolute top-0 right-6 w-6 h-6 rounded-full border border-white flex items-center justify-center z-10'
          >
            <X className='w-4 h-4 text-white' />
          </button>

          <div className='p-5 space-y-10'>
            {/* Header */}
            <div className='space-y-5'>
              <h2 className='text-2xl font-bold leading-tight'>
                Upgrade to
                <br />
                PixPretty Premium
              </h2>

              {/* Billing Toggle */}
              <div className='flex rounded-xl overflow-hidden'>
                <button
                  onClick={() => onSelectBilling('monthly')}
                  className={cn(
                    'px-12 py-2 text-base font-light transition-colors',
                    selectedBilling === 'monthly'
                      ? 'bg-white text-gray-900 rounded-l-xl'
                      : 'bg-transparent text-white'
                  )}
                >
                  Monthly
                </button>
                <button
                  onClick={() => onSelectBilling('yearly')}
                  className={cn(
                    'px-14 py-3 text-base font-light transition-colors relative',
                    selectedBilling === 'yearly'
                      ? 'bg-gray-700 text-white rounded-r-xl border border-white/20'
                      : 'bg-transparent text-white'
                  )}
                >
                  Yearly
                  {selectedBilling === 'yearly' && (
                    <span className='absolute -top-1 right-2 text-xs text-orange-400 drop-shadow-sm'>
                      Save 25%
                    </span>
                  )}
                </button>
              </div>

              {/* Plan Cards */}
              <div className='space-y-3'>
                {/* Pro Plan */}
                <div
                  className={cn(
                    'p-3 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-800 border-gray-600'
                  )}
                  onClick={() => onSelectPlan('pro')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3 className='text-xl font-bold'>Pro</h3>
                      <p className='text-xs font-light text-gray-300'>
                        {PLANS_CONFIG.pro[selectedBilling].originalPrice}
                      </p>
                    </div>
                    <div className='text-base font-light'>
                      {PLANS_CONFIG.pro[selectedBilling].price}
                    </div>
                  </div>
                </div>

                {/* Pro+ Plan */}
                <div
                  className={cn(
                    'p-4 rounded-xl border cursor-pointer transition-all',
                    selectedPlan === 'pro-plus'
                      ? 'bg-yellow-50/5 border-yellow-400'
                      : 'bg-gray-700 border-gray-500'
                  )}
                  onClick={() => onSelectPlan('pro-plus')}
                >
                  <div className='flex justify-between items-center'>
                    <div>
                      <h3 className='text-xl font-bold'>Pro+</h3>
                      <p className='text-xs font-light text-gray-300'>
                        {
                          PLANS_CONFIG['pro-plus'][selectedBilling]
                            .originalPrice
                        }
                      </p>
                    </div>
                    <div className='text-base font-light'>
                      {PLANS_CONFIG['pro-plus'][selectedBilling].price}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Purchase Button */}
            <div className='space-y-2'>
              <Button
                onClick={handlePurchase}
                disabled={isLoading}
                className='w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-light text-base py-3.5 rounded-full shadow-[0px_4px_20px_0px_rgba(0,0,0,0.25)]'
              >
                {isLoading ? 'Processing...' : 'Buy Now'}
              </Button>
              <p className='text-sm font-light text-gray-400 text-center'>
                Not sure yet?
                <br />
                Check out our Pricing Page for details.
              </p>
            </div>
          </div>

          {/* Bottom Features Section */}
          <div className='bg-white text-gray-900 p-3 rounded-b-[24px]'>
            <div className='grid grid-cols-3 gap-4 text-xs'>
              {/* Features Column */}
              <div>
                <h4 className='font-bold mb-5'>Features</h4>
                <div className='space-y-3'>
                  {PLANS_CONFIG['pro-plus'].features
                    .slice(0, 6)
                    .map((feature, index) => (
                      <div key={index} className='leading-tight'>
                        {feature.length > 25
                          ? `${feature.substring(0, 25)}...`
                          : feature}
                      </div>
                    ))}
                </div>
              </div>

              {/* Pro Column */}
              <div className='text-center'>
                <h4 className='font-bold mb-5'>Pro</h4>
                <div className='space-y-3'>
                  {[1, 2, 3, 4].map((_, index) => (
                    <div key={index} className='flex justify-center py-1'>
                      <X className='w-6 h-6 text-gray-900' strokeWidth={1.5} />
                    </div>
                  ))}
                  <div className='py-1'>
                    <span className='font-bold'>2G</span>
                  </div>
                  <div className='flex justify-center py-1'>
                    <X className='w-6 h-6 text-gray-900' strokeWidth={1.5} />
                  </div>
                </div>
              </div>

              {/* Pro+ Column */}
              <div className='text-center bg-yellow-50 border border-orange-200 rounded-xl p-2'>
                <h4 className='font-bold mb-5'>Pro+</h4>
                <div className='space-y-3'>
                  {[1, 2, 3, 4, 6].map((_, index) => (
                    <div key={index} className='flex justify-center py-1'>
                      <Check
                        className='w-6 h-6 text-gray-900'
                        strokeWidth={2}
                      />
                    </div>
                  ))}
                  <div className='py-1'>
                    <span className='font-bold'>10G</span>
                  </div>
                  <div className='flex justify-center py-1'>
                    <Check className='w-6 h-6 text-gray-900' strokeWidth={2} />
                  </div>
                </div>
              </div>
            </div>

            {/* Scroll Indicator */}
            <div className='flex justify-end mt-4'>
              <div className='w-1.5 h-20 bg-gray-200 rounded-full relative'>
                <div className='w-1.5 h-5 bg-gray-400 rounded-full absolute top-0' />
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
