'use client';

import React from 'react';
import { Header } from './Header';
import { PurchaseButton } from './PurchaseModalExample';
import { accountInfoStore } from '@/store/accountStore';
import { useTranslations } from 'next-intl';

/**
 * 集成了购买功能的Header组件示例
 * 展示如何在现有Header中添加购买按钮
 */
export function HeaderWithPurchase({ 
  className,
  variant = 'desktop' 
}: {
  className?: string;
  variant?: 'desktop' | 'batch-editor' | 'mobile';
}) {
  const t = useTranslations('purchase');
  const userInfo = accountInfoStore((state) => state.userInfo);
  
  // 检查用户是否已经是付费用户
  const isPremiumUser = userInfo?.isPremium || false;
  const currentPlan = userInfo?.plan || 'free';

  return (
    <div className="relative">
      <Header className={className} variant={variant} />
      
      {/* 如果用户已登录但不是付费用户，显示升级按钮 */}
      {userInfo && !isPremiumUser && (
        <div className="absolute top-2 right-20 z-10">
          <PurchaseButton
            plan="pro-plus"
            size="sm"
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 shadow-lg"
          >
            {t('upgrade')}
          </PurchaseButton>
        </div>
      )}
      
      {/* 如果是Pro用户，可以显示升级到Pro+的按钮 */}
      {userInfo && currentPlan === 'pro' && (
        <div className="absolute top-2 right-20 z-10">
          <PurchaseButton
            plan="pro-plus"
            size="sm"
            className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-gray-900 shadow-lg"
          >
            {t('upgradeToProPlus')}
          </PurchaseButton>
        </div>
      )}
    </div>
  );
}

/**
 * 简化版的Header集成示例
 * 直接在Header内部集成购买功能
 */
export function SimpleHeaderWithPurchase() {
  const userInfo = accountInfoStore((state) => state.userInfo);
  const isPremiumUser = userInfo?.isPremium || false;

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-gray-900">PixPretty</h1>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-gray-500 hover:text-gray-900">
              Background Remover
            </a>
            <a href="#" className="text-gray-500 hover:text-gray-900">
              Batch Editor
            </a>
            <a href="#" className="text-gray-500 hover:text-gray-900">
              Tools
            </a>
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {userInfo ? (
              <>
                {/* Credits display */}
                <div className="text-sm text-gray-600">
                  Credits: {userInfo.score || 0}
                </div>
                
                {/* Purchase button for non-premium users */}
                {!isPremiumUser && (
                  <PurchaseButton
                    plan="pro-plus"
                    size="sm"
                    className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                  >
                    Upgrade
                  </PurchaseButton>
                )}
                
                {/* User avatar/menu */}
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              </>
            ) : (
              <>
                <button className="text-gray-500 hover:text-gray-900">
                  Sign In
                </button>
                <PurchaseButton
                  plan="pro-plus"
                  size="sm"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900"
                >
                  Get Started
                </PurchaseButton>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

/**
 * 移动端Header集成示例
 */
export function MobileHeaderWithPurchase() {
  const userInfo = accountInfoStore((state) => state.userInfo);
  const isPremiumUser = userInfo?.isPremium || false;

  return (
    <header className="bg-white border-b border-gray-200 md:hidden">
      <div className="px-4">
        <div className="flex justify-between items-center h-14">
          {/* Logo */}
          <h1 className="text-lg font-bold text-gray-900">PixPretty</h1>

          {/* Right side */}
          <div className="flex items-center space-x-2">
            {userInfo && !isPremiumUser && (
              <PurchaseButton
                plan="pro-plus"
                size="sm"
                className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 text-xs px-3 py-1"
              >
                Upgrade
              </PurchaseButton>
            )}
            
            {/* Menu button */}
            <button className="p-2">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
